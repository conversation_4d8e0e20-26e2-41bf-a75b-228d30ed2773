#!/usr/bin/env python3
"""
Test script đơn giản để tạo ví Sui
"""

import hashlib
import secrets
from mnemonic import Mnemonic

def generate_simple_wallet():
    """Tạo ví Sui đơn giản"""
    # Tạo mnemonic
    mnemo = Mnemonic("english")
    phrase = mnemo.generate(strength=128)
    
    # Tạo private key từ mnemonic
    private_key = hashlib.sha256(phrase.encode()).hexdigest()
    
    # Tạo địa chỉ đơn giản
    address = "0x" + private_key[:40]
    
    return {
        "address": address,
        "private_key": private_key,
        "mnemonic": phrase
    }

def main():
    print("🚀 Test tạo ví Sui đơn giản")
    print("=" * 50)
    
    try:
        # Tạo 3 ví test
        for i in range(3):
            wallet = generate_simple_wallet()
            print(f"\nVÍ #{i+1}:")
            print(f"Địa chỉ: {wallet['address']}")
            print(f"Private Key: {wallet['private_key']}")
            print(f"Mnemonic: {wallet['mnemonic']}")
            print("-" * 50)
        
        print("✅ Test thành công!")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
