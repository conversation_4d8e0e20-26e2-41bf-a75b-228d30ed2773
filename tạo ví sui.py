"""
TẠO 60 VÍ SUI CÙNG LÚC - 2 CHẾ ĐỘ
Cài đặt thư viện: pip install mnemonic bip_utils
"""

import csv
import hashlib
from mnemonic import Mnemonic
from bip_utils import Bip39SeedGenerator, Bip44, Bip44<PERSON>oi<PERSON>

def generate_wallets(num, mode="basic"):
    wallets = []
    mnemo = Mnemonic("english")
    
    for _ in range(num):
        # Tạo mnemonic
        phrase = mnemo.generate(strength=128)
        
        # Chế độ Basic (SHA256)
        if mode == "basic":
            priv_key = hashlib.sha256(phrase.encode()).hexdigest()
            address = priv_key[:40]
        
        # Chế độ BIP-39 (An toàn)
        elif mode == "secure":
            seed = Bip39SeedGenerator(phrase).Generate()
            bip44_ctx = Bip44.FromSeed(seed, Bip44Coins.SUI)
            priv_key = bip44_ctx.PrivateKey().Raw().hex()
            address = bip44_ctx.PublicKey().ToAddress()
        
        wallets.append({
            "address": address,
            "private_key": priv_key,
            "mnemonic": phrase
        })
    
    return wallets

# ===== CẤU HÌNH =====
NUM_WALLETS = 60
MODE = "basic"  # "basic" hoặc "secure"

# Tạo ví
result = generate_wallets(NUM_WALLETS, MODE)

# Xuất file CSV
with open('sui_wallets.csv', 'w', newline='') as f:
    writer = csv.DictWriter(f, fieldnames=['address','private_key','mnemonic'])
    writer.writeheader()
    writer.writerows(result)

print(f"✅ Đã tạo thành công {NUM_WALLETS} ví Sui vào file sui_wallets.csv")
