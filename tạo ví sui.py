"""
SUI WALLET GENERATOR - CÔNG CỤ TẠO VÍ SUI HÀNG LOẠT
Phiên bản nâng cao với nhiều tính năng và bảo mật cao

Cài đặt thư viện: pip install -r requirements.txt
Sử dụng: python "tạo ví sui.py" --help
"""

import csv
import json
import argparse
import hashlib
import os
import sys
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path

try:
    from mnemonic import Mnemonic
    from tqdm import tqdm
    import secrets
    import base58
    from ecdsa import SigningKey, SECP256k1
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
except ImportError as e:
    print(f"❌ Lỗi import thư viện: {e}")
    print("Vui lòng cài đặt: pip install -r requirements.txt")
    sys.exit(1)

class SuiWalletGenerator:
    """Lớp tạo ví Sui với nhiều chế độ bảo mật"""

    def __init__(self):
        self.mnemo = Mnemonic("english")

    def generate_secure_wallet(self) -> Dict[str, str]:
        """Tạo ví Sui theo chuẩn BIP-39 (Khuyến nghị)"""
        # Tạo mnemonic 12 từ (128-bit entropy)
        phrase = self.mnemo.generate(strength=128)

        # Tạo seed từ mnemonic với PBKDF2
        seed = self._mnemonic_to_seed(phrase)

        # Tạo private key từ seed
        private_key_bytes = self._derive_private_key(seed)
        private_key = private_key_bytes.hex()

        # Tạo public key từ private key
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        public_key_bytes = signing_key.verifying_key.to_string("compressed")
        public_key = public_key_bytes.hex()

        # Tạo địa chỉ Sui (0x + blake2b hash của public key)
        address = "0x" + hashlib.blake2b(
            public_key_bytes,
            digest_size=20
        ).hexdigest()

        return {
            "address": address,
            "private_key": private_key,
            "mnemonic": phrase,
            "public_key": public_key
        }

    def _mnemonic_to_seed(self, mnemonic: str, passphrase: str = "") -> bytes:
        """Chuyển mnemonic thành seed theo chuẩn BIP-39"""
        # Normalize mnemonic
        mnemonic_bytes = mnemonic.encode('utf-8')
        salt = ("mnemonic" + passphrase).encode('utf-8')

        # Sử dụng PBKDF2 với 2048 iterations
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA512(),
            length=64,
            salt=salt,
            iterations=2048,
        )
        seed = kdf.derive(mnemonic_bytes)
        return seed

    def _derive_private_key(self, seed: bytes) -> bytes:
        """Tạo private key từ seed"""
        # Sử dụng HMAC-SHA512 để derive private key
        import hmac

        # Tạo master key
        master_key = hmac.new(
            b"ed25519 seed",
            seed[:32],
            hashlib.sha512
        ).digest()

        # Lấy 32 bytes đầu làm private key
        return master_key[:32]

    def generate_basic_wallet(self) -> Dict[str, str]:
        """Tạo ví Sui cơ bản (Chỉ dùng cho test)"""
        # Tạo mnemonic
        phrase = self.mnemo.generate(strength=128)

        # Tạo private key từ SHA256 của mnemonic
        private_key = hashlib.sha256(phrase.encode()).hexdigest()

        # Tạo địa chỉ đơn giản (chỉ dùng cho demo)
        address = "0x" + private_key[:40]

        return {
            "address": address,
            "private_key": private_key,
            "mnemonic": phrase,
            "public_key": "N/A"
        }

    def generate_wallets_batch(self, count: int, mode: str = "secure",
                              progress_callback: Optional[callable] = None) -> List[Dict[str, str]]:
        """Tạo hàng loạt ví với progress tracking"""
        wallets = []

        # Chọn phương thức tạo ví
        generator_func = (self.generate_secure_wallet if mode == "secure"
                         else self.generate_basic_wallet)

        # Tạo progress bar nếu có tqdm
        if progress_callback:
            iterator = tqdm(range(count), desc=f"Đang tạo {count} ví Sui")
        else:
            iterator = range(count)

        for i in iterator:
            try:
                wallet = generator_func()
                wallet["index"] = i + 1
                wallet["created_at"] = datetime.now().isoformat()
                wallets.append(wallet)

                if progress_callback:
                    progress_callback(i + 1, count)

            except Exception as e:
                print(f"❌ Lỗi tạo ví #{i+1}: {e}")
                continue

        return wallets

class WalletExporter:
    """Lớp xuất ví ra nhiều định dạng file"""

    @staticmethod
    def export_csv(wallets: List[Dict], filename: str):
        """Xuất ra file CSV"""
        fieldnames = ['index', 'address', 'private_key', 'mnemonic', 'public_key', 'created_at']

        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(wallets)

    @staticmethod
    def export_json(wallets: List[Dict], filename: str):
        """Xuất ra file JSON"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(wallets, f, indent=2, ensure_ascii=False)

    @staticmethod
    def export_txt(wallets: List[Dict], filename: str):
        """Xuất ra file TXT dễ đọc"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("SUI WALLET GENERATOR - DANH SÁCH VÍ\n")
            f.write(f"Tạo lúc: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Tổng số ví: {len(wallets)}\n")
            f.write("=" * 80 + "\n\n")

            for wallet in wallets:
                f.write(f"VÍ #{wallet['index']}\n")
                f.write(f"Địa chỉ: {wallet['address']}\n")
                f.write(f"Private Key: {wallet['private_key']}\n")
                f.write(f"Mnemonic: {wallet['mnemonic']}\n")
                if wallet.get('public_key') != 'N/A':
                    f.write(f"Public Key: {wallet['public_key']}\n")
                f.write(f"Tạo lúc: {wallet['created_at']}\n")
                f.write("-" * 80 + "\n\n")

def main():
    """Hàm main với giao diện dòng lệnh"""
    parser = argparse.ArgumentParser(
        description="Công cụ tạo ví Sui hàng loạt",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ví dụ sử dụng:
  python "tạo ví sui.py" -n 100 -m secure -f csv
  python "tạo ví sui.py" -n 1000 -m secure -f json -o my_wallets
  python "tạo ví sui.py" -n 50 -m basic -f txt
        """
    )

    parser.add_argument('-n', '--number', type=int, default=60,
                       help='Số lượng ví cần tạo (mặc định: 60)')

    parser.add_argument('-m', '--mode', choices=['secure', 'basic'], default='secure',
                       help='Chế độ tạo ví: secure (BIP-39, khuyến nghị) hoặc basic (đơn giản)')

    parser.add_argument('-f', '--format', choices=['csv', 'json', 'txt', 'all'], default='csv',
                       help='Định dạng file xuất: csv, json, txt hoặc all')

    parser.add_argument('-o', '--output', type=str, default='sui_wallets',
                       help='Tên file xuất (không cần phần mở rộng)')

    parser.add_argument('--no-progress', action='store_true',
                       help='Tắt thanh tiến trình')

    args = parser.parse_args()

    # Kiểm tra số lượng ví
    if args.number <= 0:
        print("❌ Số lượng ví phải lớn hơn 0")
        return

    if args.number > 10000:
        confirm = input(f"⚠️  Bạn muốn tạo {args.number} ví. Điều này có thể mất nhiều thời gian. Tiếp tục? (y/N): ")
        if confirm.lower() != 'y':
            print("Đã hủy.")
            return

    print("🚀 SUI WALLET GENERATOR")
    print("=" * 50)
    print(f"Số lượng ví: {args.number}")
    print(f"Chế độ: {args.mode.upper()}")
    print(f"Định dạng xuất: {args.format.upper()}")
    print("=" * 50)

    # Tạo ví
    generator = SuiWalletGenerator()

    try:
        print("⏳ Đang tạo ví...")
        start_time = datetime.now()

        wallets = generator.generate_wallets_batch(
            count=args.number,
            mode=args.mode,
            progress_callback=None if args.no_progress else lambda x, y: None
        )

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        if not wallets:
            print("❌ Không tạo được ví nào!")
            return

        print(f"✅ Đã tạo thành công {len(wallets)} ví trong {duration:.2f} giây")

        # Xuất file
        exporter = WalletExporter()

        if args.format == 'all':
            formats = ['csv', 'json', 'txt']
        else:
            formats = [args.format]

        for fmt in formats:
            filename = f"{args.output}.{fmt}"

            if fmt == 'csv':
                exporter.export_csv(wallets, filename)
            elif fmt == 'json':
                exporter.export_json(wallets, filename)
            elif fmt == 'txt':
                exporter.export_txt(wallets, filename)

            print(f"📁 Đã xuất file: {filename}")

        # Thống kê
        print("\n📊 THỐNG KÊ:")
        print(f"• Tổng ví tạo: {len(wallets)}")
        print(f"• Thời gian: {duration:.2f} giây")
        print(f"• Tốc độ: {len(wallets)/duration:.1f} ví/giây")

        if args.mode == 'secure':
            print("• Bảo mật: Cao (BIP-39/BIP-44)")
        else:
            print("• Bảo mật: Cơ bản (chỉ dùng test)")

        print("\n⚠️  LƯU Ý BẢO MẬT:")
        print("• Giữ bí mật private key và mnemonic")
        print("• Không chia sẻ thông tin ví với người khác")
        print("• Sao lưu file ví ở nơi an toàn")

    except KeyboardInterrupt:
        print("\n❌ Đã dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
