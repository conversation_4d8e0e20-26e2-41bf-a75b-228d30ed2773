#!/usr/bin/env python3
"""
SUI WALLET GENERATOR - CÔNG CỤ TẠO VÍ SUI HÀNG LOẠT
Phiên bản đơn giản, <PERSON><PERSON> định và dễ sử dụng

Cài đặt: pip install mnemonic
Sử dụng: python sui_wallet_generator.py
"""

import csv
import json
import hashlib
import secrets
import os
from datetime import datetime
from typing import List, Dict

try:
    from mnemonic import Mnemonic
except ImportError:
    print("❌ Cần cài đặt thư viện mnemonic:")
    print("pip install mnemonic")
    exit(1)

class SuiWalletGenerator:
    """Lớp tạo ví Sui đơn giản và hiệu quả"""
    
    def __init__(self):
        self.mnemo = Mnemonic("english")
    
    def generate_wallet_secure(self) -> Dict[str, str]:
        """Tạo ví Sui bảo mật với mnemonic chuẩn BIP-39"""
        # Tạo mnemonic 12 từ
        phrase = self.mnemo.generate(strength=128)
        
        # Tạo seed từ mnemonic
        seed = self.mnemo.to_seed(phrase)
        
        # Tạo private key từ seed
        private_key = hashlib.sha256(seed).hexdigest()
        
        # Tạo địa chỉ Sui (0x + 40 ký tự hex)
        address_hash = hashlib.sha256(private_key.encode()).hexdigest()
        address = "0x" + address_hash[:40]
        
        return {
            "address": address,
            "private_key": private_key,
            "mnemonic": phrase,
            "seed": seed.hex()
        }
    
    def generate_wallet_basic(self) -> Dict[str, str]:
        """Tạo ví Sui cơ bản (nhanh, dùng cho test)"""
        # Tạo mnemonic
        phrase = self.mnemo.generate(strength=128)
        
        # Tạo private key đơn giản
        private_key = hashlib.sha256(phrase.encode()).hexdigest()
        
        # Tạo địa chỉ
        address = "0x" + private_key[:40]
        
        return {
            "address": address,
            "private_key": private_key,
            "mnemonic": phrase,
            "seed": "N/A"
        }
    
    def generate_batch(self, count: int, mode: str = "secure") -> List[Dict[str, str]]:
        """Tạo hàng loạt ví"""
        wallets = []
        
        print(f"⏳ Đang tạo {count} ví Sui (chế độ: {mode.upper()})...")
        
        for i in range(count):
            try:
                if mode == "secure":
                    wallet = self.generate_wallet_secure()
                else:
                    wallet = self.generate_wallet_basic()
                
                wallet["index"] = i + 1
                wallet["created_at"] = datetime.now().isoformat()
                wallets.append(wallet)
                
                # Hiển thị tiến trình
                if (i + 1) % 100 == 0 or i + 1 == count:
                    print(f"  ✅ Đã tạo {i + 1}/{count} ví")
                    
            except Exception as e:
                print(f"  ❌ Lỗi tạo ví #{i+1}: {e}")
                continue
        
        return wallets

class WalletExporter:
    """Lớp xuất ví ra file"""
    
    @staticmethod
    def export_csv(wallets: List[Dict], filename: str):
        """Xuất ra CSV"""
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            if wallets:
                writer = csv.DictWriter(f, fieldnames=wallets[0].keys())
                writer.writeheader()
                writer.writerows(wallets)
    
    @staticmethod
    def export_json(wallets: List[Dict], filename: str):
        """Xuất ra JSON"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(wallets, f, indent=2, ensure_ascii=False)
    
    @staticmethod
    def export_txt(wallets: List[Dict], filename: str):
        """Xuất ra TXT dễ đọc"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("SUI WALLET GENERATOR - DANH SÁCH VÍ\n")
            f.write(f"Tạo lúc: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Tổng số ví: {len(wallets)}\n")
            f.write("=" * 80 + "\n\n")
            
            for wallet in wallets:
                f.write(f"VÍ #{wallet['index']}\n")
                f.write(f"Địa chỉ: {wallet['address']}\n")
                f.write(f"Private Key: {wallet['private_key']}\n")
                f.write(f"Mnemonic: {wallet['mnemonic']}\n")
                if wallet.get('seed') != 'N/A':
                    f.write(f"Seed: {wallet['seed']}\n")
                f.write(f"Tạo lúc: {wallet['created_at']}\n")
                f.write("-" * 80 + "\n\n")

def main():
    """Hàm main với menu tương tác"""
    print("🚀 SUI WALLET GENERATOR")
    print("=" * 60)
    print("Công cụ tạo ví Sui hàng loạt")
    print("=" * 60)
    
    try:
        # Nhập số lượng ví
        while True:
            try:
                count = int(input("📊 Nhập số lượng ví cần tạo (1-10000): "))
                if 1 <= count <= 10000:
                    break
                else:
                    print("❌ Vui lòng nhập số từ 1 đến 10000")
            except ValueError:
                print("❌ Vui lòng nhập số hợp lệ")
        
        # Chọn chế độ
        print("\n🔧 Chọn chế độ tạo ví:")
        print("1. Secure (Bảo mật cao, khuyến nghị)")
        print("2. Basic (Nhanh, dùng cho test)")
        
        while True:
            choice = input("Chọn (1/2): ").strip()
            if choice == "1":
                mode = "secure"
                break
            elif choice == "2":
                mode = "basic"
                break
            else:
                print("❌ Vui lòng chọn 1 hoặc 2")
        
        # Chọn định dạng xuất
        print("\n📁 Chọn định dạng file xuất:")
        print("1. CSV (Excel)")
        print("2. JSON (Lập trình)")
        print("3. TXT (Dễ đọc)")
        print("4. Tất cả")
        
        while True:
            choice = input("Chọn (1/2/3/4): ").strip()
            if choice in ["1", "2", "3", "4"]:
                format_choice = choice
                break
            else:
                print("❌ Vui lòng chọn 1, 2, 3 hoặc 4")
        
        # Tạo ví
        generator = SuiWalletGenerator()
        start_time = datetime.now()
        
        wallets = generator.generate_batch(count, mode)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if not wallets:
            print("❌ Không tạo được ví nào!")
            return
        
        print(f"\n✅ Đã tạo thành công {len(wallets)} ví trong {duration:.2f} giây")
        
        # Xuất file
        exporter = WalletExporter()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        formats = []
        if format_choice == "1":
            formats = ["csv"]
        elif format_choice == "2":
            formats = ["json"]
        elif format_choice == "3":
            formats = ["txt"]
        else:
            formats = ["csv", "json", "txt"]
        
        for fmt in formats:
            filename = f"sui_wallets_{timestamp}.{fmt}"
            
            if fmt == "csv":
                exporter.export_csv(wallets, filename)
            elif fmt == "json":
                exporter.export_json(wallets, filename)
            elif fmt == "txt":
                exporter.export_txt(wallets, filename)
            
            print(f"📁 Đã xuất file: {filename}")
        
        # Thống kê
        print(f"\n📊 THỐNG KÊ:")
        print(f"• Tổng ví tạo: {len(wallets)}")
        print(f"• Thời gian: {duration:.2f} giây")
        print(f"• Tốc độ: {len(wallets)/duration:.1f} ví/giây")
        print(f"• Chế độ: {mode.upper()}")
        
        print(f"\n⚠️  LƯU Ý BẢO MẬT:")
        print("• Giữ bí mật private key và mnemonic")
        print("• Không chia sẻ thông tin ví với người khác")
        print("• Sao lưu file ví ở nơi an toàn")
        print("• Không upload lên internet/cloud")
        
    except KeyboardInterrupt:
        print("\n❌ Đã dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
