#!/usr/bin/env python3
"""
DEMO SCRIPT - Ví dụ sử dụng SUI WALLET GENERATOR
Chạy script này để xem các ví dụ tạo ví Sui
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """Chạy lệnh và hiển thị kết quả"""
    print(f"\n{'='*60}")
    print(f"🔥 {description}")
    print(f"{'='*60}")
    print(f"Lệnh: {cmd}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8')
        
        if result.stdout:
            print("📤 OUTPUT:")
            print(result.stdout)
        
        if result.stderr:
            print("⚠️ ERRORS:")
            print(result.stderr)
            
        if result.returncode == 0:
            print("✅ Thành công!")
        else:
            print(f"❌ Lỗi (exit code: {result.returncode})")
            
    except Exception as e:
        print(f"❌ Lỗi chạy lệnh: {e}")

def main():
    """Chạy các demo"""
    print("🚀 SUI WALLET GENERATOR - DEMO")
    print("Đây là các ví dụ sử dụng tool tạo ví Sui")
    
    # Kiểm tra file tồn tại
    wallet_script = "tạo ví sui.py"
    if not os.path.exists(wallet_script):
        print(f"❌ Không tìm thấy file: {wallet_script}")
        return
    
    demos = [
        {
            "cmd": f'python "{wallet_script}" --help',
            "desc": "Hiển thị hướng dẫn sử dụng"
        },
        {
            "cmd": f'python "{wallet_script}" -n 5 -m secure -f csv -o demo_secure',
            "desc": "Tạo 5 ví bảo mật cao, xuất CSV"
        },
        {
            "cmd": f'python "{wallet_script}" -n 3 -m basic -f json -o demo_basic',
            "desc": "Tạo 3 ví cơ bản, xuất JSON"
        },
        {
            "cmd": f'python "{wallet_script}" -n 2 -m secure -f txt -o demo_readable',
            "desc": "Tạo 2 ví bảo mật, xuất TXT dễ đọc"
        }
    ]
    
    for i, demo in enumerate(demos, 1):
        print(f"\n🎯 DEMO {i}/{len(demos)}")
        run_command(demo["cmd"], demo["desc"])
        
        if i < len(demos):
            input("\n⏸️ Nhấn Enter để tiếp tục demo tiếp theo...")
    
    print(f"\n{'='*60}")
    print("🎉 HOÀN THÀNH TẤT CẢ DEMO!")
    print("📁 Kiểm tra các file đã tạo:")
    
    # Liệt kê file đã tạo
    demo_files = [
        "demo_secure.csv",
        "demo_basic.json", 
        "demo_readable.txt"
    ]
    
    for file in demo_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  ✅ {file} ({size} bytes)")
        else:
            print(f"  ❌ {file} (không tồn tại)")
    
    print("\n💡 TIP: Mở các file này để xem kết quả!")
    print("⚠️ LƯU Ý: Đây chỉ là demo, không sử dụng cho mainnet!")

if __name__ == "__main__":
    main()
