# 🚀 SUI WALLET GENERATOR

Công cụ tạo ví Sui hàng loạt với tính năng nâng cao, hỗ trợ tạo hàng nghìn ví cùng lúc.

## ✨ Tính năng

- 🔐 **Bảo mật cao**: Sử dụng chuẩn BIP-39 với mnemonic
- ⚡ **Tốc độ cao**: Tạo hàng nghìn ví trong vài giây
- 📊 **Progress tracking**: Hiển thị tiến trình khi tạo số lượng lớn
- 📁 **Nhiều định dạng**: Xuất CSV, JSON, TXT
- 🛡️ **An toàn**: Validation và error handling
- 🎯 **<PERSON><PERSON> sử dụng**: Giao diện menu tương tác
- 💻 **Đa nền tảng**: Windows, Linux, macOS

## 📦 Cài đặt

### Cách 1: Tự động (Windows)
```bash
# Chạy file run.bat - sẽ tự động cài đặt và chạy
run.bat
```

### Cách 2: Th<PERSON> công
```bash
# Cài đặt Python 3.7+ từ https://python.org
# Cài đặt thư viện cần thiết
pip install mnemonic
```

## 🚀 Sử dụng

### Cách sử dụng chính (Khuyến nghị)

```bash
# Chạy tool với giao diện menu
python sui_wallet_generator.py

# Hoặc trên Windows
run.bat
```

### Demo nhanh

```bash
# Tạo 5 ví demo để test
python quick_demo.py
```

### Menu tương tác

Khi chạy tool, bạn sẽ thấy menu như sau:

```
🚀 SUI WALLET GENERATOR
============================================================
📊 Nhập số lượng ví cần tạo (1-10000): 100

🔧 Chọn chế độ tạo ví:
1. Secure (Bảo mật cao, khuyến nghị)
2. Basic (Nhanh, dùng cho test)
Chọn (1/2): 1

📁 Chọn định dạng file xuất:
1. CSV (Excel)
2. JSON (Lập trình)
3. TXT (Dễ đọc)
4. Tất cả
Chọn (1/2/3/4): 4
```

### Tool nâng cao (Command line)

```bash
# Sử dụng tool gốc với command line
python "tạo ví sui.py" -n 100 -m secure -f csv
```

## 📋 Chế độ tạo ví

### 🔐 Secure Mode (Khuyến nghị)
- Sử dụng chuẩn BIP-39/BIP-44
- Tương thích với ví Sui chính thức
- Bảo mật cao, phù hợp sử dụng thực tế

### ⚡ Basic Mode
- Tạo nhanh cho mục đích test
- Không khuyến nghị cho mainnet
- Chỉ dùng để phát triển/thử nghiệm

## 📁 Định dạng file xuất

### CSV
```csv
index,address,private_key,mnemonic,public_key,created_at
1,0x123...,abc123...,word1 word2...,def456...,2024-01-01T12:00:00
```

### JSON
```json
[
  {
    "index": 1,
    "address": "0x123...",
    "private_key": "abc123...",
    "mnemonic": "word1 word2...",
    "public_key": "def456...",
    "created_at": "2024-01-01T12:00:00"
  }
]
```

### TXT
```
VÍ #1
Địa chỉ: 0x123...
Private Key: abc123...
Mnemonic: word1 word2...
```

## ⚠️ Lưu ý bảo mật

- 🔒 **Giữ bí mật**: Không chia sẻ private key và mnemonic
- 💾 **Sao lưu**: Lưu file ví ở nơi an toàn
- 🚫 **Không upload**: Không đưa lên internet/cloud
- 🔐 **Mã hóa**: Nên mã hóa file ví trước khi lưu trữ

## 🛠️ Yêu cầu hệ thống

- Python 3.7+
- Windows/Linux/macOS
- RAM: Tối thiểu 512MB (cho 10,000+ ví)

## 📈 Hiệu suất

- **Tốc độ**: ~1000-5000 ví/giây (tùy máy)
- **Bộ nhớ**: ~1KB/ví
- **Hỗ trợ**: Lên đến hàng triệu ví

## 🐛 Báo lỗi

Nếu gặp lỗi, vui lòng cung cấp:
- Phiên bản Python
- Hệ điều hành
- Lệnh đã chạy
- Thông báo lỗi đầy đủ

## 📄 License

MIT License - Sử dụng tự do cho mục đích cá nhân và thương mại.
