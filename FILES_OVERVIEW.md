# 📁 TỔNG QUAN CÁC FILE TRONG PROJECT

## 🎯 File chính để sử dụng

### 1. `sui_wallet_generator.py` ⭐ (KHUYẾN NGHỊ)
- **Mô tả**: Tool chính với giao diện menu thân thiện
- **Cách dùng**: `python sui_wallet_generator.py`
- **Tính năng**: <PERSON><PERSON>, d<PERSON> sử dụ<PERSON>, <PERSON><PERSON> định
- **<PERSON><PERSON> hợp**: Người dùng thông thường

### 2. `run.bat` ⭐ (CHO WINDOWS)
- **Mô tả**: Script tự động cài đặt và chạy tool
- **Cách dùng**: Double-click hoặc `run.bat`
- **Tính năng**: Tự động kiểm tra Python, cài đặt thư viện
- **<PERSON><PERSON> hợp**: Người dùng Windows muốn dễ dàng

### 3. `quick_demo.py` 🧪 (DEMO)
- **<PERSON><PERSON> tả**: Demo nhanh tạo 5 ví để test
- **<PERSON><PERSON>ch dùng**: `python quick_demo.py`
- **<PERSON><PERSON>h năng**: <PERSON> nhanh, kiểm tra tool hoạt động
- **Phù hợp**: Kiểm tra trước khi sử dụng chính thức

## 🔧 File nâng cao

### 4. `tạo ví sui.py` 🚀 (NÂNG CAO)
- **Mô tả**: Tool gốc với command line interface
- **Cách dùng**: `python "tạo ví sui.py" --help`
- **Tính năng**: Nhiều tùy chọn, command line
- **Phù hợp**: Lập trình viên, người dùng nâng cao

### 5. `demo.py` 📋 (DEMO NÂNG CAO)
- **Mô tả**: Demo đầy đủ các tính năng của tool gốc
- **Cách dùng**: `python demo.py`
- **Tính năng**: Chạy nhiều ví dụ khác nhau
- **Phù hợp**: Tìm hiểu đầy đủ tính năng

### 6. `test_simple.py` 🧪 (TEST ĐỠN GIẢN)
- **Mô tả**: Test script đơn giản nhất
- **Cách dùng**: `python test_simple.py`
- **Tính năng**: Test cơ bản, debug
- **Phù hợp**: Kiểm tra lỗi, debug

## 📄 File tài liệu

### 7. `README.md` 📖
- **Mô tả**: Hướng dẫn sử dụng chi tiết
- **Nội dung**: Cài đặt, sử dụng, ví dụ

### 8. `requirements.txt` 📦
- **Mô tả**: Danh sách thư viện cần thiết
- **Cách dùng**: `pip install -r requirements.txt`

### 9. `FILES_OVERVIEW.md` 📋 (File này)
- **Mô tả**: Tổng quan tất cả file trong project

## 🎯 KHUYẾN NGHỊ SỬ DỤNG

### Cho người dùng thông thường:
1. **Windows**: Chạy `run.bat`
2. **Linux/Mac**: Chạy `python sui_wallet_generator.py`

### Cho lập trình viên:
1. **Test nhanh**: `python quick_demo.py`
2. **Sử dụng nâng cao**: `python "tạo ví sui.py" --help`
3. **Tích hợp code**: Import từ `sui_wallet_generator.py`

### Cho người muốn tìm hiểu:
1. **Đọc**: `README.md`
2. **Demo**: `python demo.py`
3. **Test**: `python test_simple.py`

## 🔄 Quy trình sử dụng khuyến nghị

```
1. Đọc README.md
2. Chạy quick_demo.py để test
3. Sử dụng sui_wallet_generator.py hoặc run.bat
4. Kiểm tra file output được tạo
5. Sao lưu file ví an toàn
```

## ⚠️ Lưu ý bảo mật

- **Không upload** file ví lên internet
- **Sao lưu** file ví ở nơi an toàn
- **Mã hóa** file ví trước khi lưu trữ
- **Kiểm tra** kỹ trước khi sử dụng mainnet

## 🆘 Hỗ trợ

Nếu gặp vấn đề:
1. Chạy `quick_demo.py` để test
2. Kiểm tra Python version >= 3.7
3. Cài đặt lại: `pip install mnemonic`
4. Đọc error message cẩn thận
