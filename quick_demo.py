#!/usr/bin/env python3
"""
QUICK DEMO - Tạo nhanh 5 ví Sui để test
"""

import sys
import os

# Thê<PERSON> thư mục hiện tại vào path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from sui_wallet_generator import SuiWalletGenerator, WalletExporter
    from datetime import datetime
except ImportError as e:
    print(f"❌ Lỗi import: {e}")
    print("Vui lòng cài đặt: pip install mnemonic")
    exit(1)

def main():
    print("🚀 QUICK DEMO - SUI WALLET GENERATOR")
    print("=" * 50)
    print("Tạo nhanh 5 ví Sui để test...")
    print("=" * 50)
    
    try:
        # Tạo generator
        generator = SuiWalletGenerator()
        
        # Tạo 5 ví secure
        print("\n🔐 Tạo 5 ví SECURE:")
        wallets_secure = generator.generate_batch(5, "secure")
        
        # <PERSON><PERSON><PERSON> thị kết quả
        for wallet in wallets_secure:
            print(f"\nVÍ #{wallet['index']}:")
            print(f"  Địa chỉ: {wallet['address']}")
            print(f"  Private Key: {wallet['private_key'][:20]}...")
            print(f"  Mnemonic: {wallet['mnemonic']}")
        
        # Xuất file demo
        exporter = WalletExporter()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        filename = f"demo_wallets_{timestamp}.csv"
        exporter.export_csv(wallets_secure, filename)
        
        print(f"\n✅ Đã xuất file demo: {filename}")
        print(f"📊 Tổng cộng: {len(wallets_secure)} ví")
        
        print("\n⚠️  LƯU Ý:")
        print("• Đây chỉ là demo, không sử dụng cho mainnet!")
        print("• File CSV đã được tạo để bạn kiểm tra")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
